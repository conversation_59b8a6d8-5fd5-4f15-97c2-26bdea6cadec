# 通话打断功能修复说明

## 问题描述
用户反馈：机器人正在读，然后我打断了，但朗读没有停止就"正在听"了

## 问题分析
原来的`callAbort`函数存在以下问题：
1. **异步操作顺序不当**: 先调用`abort()`，再停止ST，最后才重启ST
2. **TTS停止不彻底**: `abort()`函数中的`tts.value?.stopTTS()`是异步的，但没有等待完成
3. **状态切换过快**: 在TTS还没完全停止时就启动了ST，导致"朗读没停止就开始听"

## 修复方案

### 1. 改进callAbort函数
```javascript
const callAbort = async () => {
  try {
    setCallStatus("")
    
    // 先停止TTS播放，确保朗读完全停止
    await tts.value?.stopTTS()
    
    // 停止生成和清理队列
    abort()
    
    // 停止语音识别
    await st.value?.stopST()
    
    // 等待所有资源清理完成后再重启ST
    setTimeout(() => {
      if (callVisible.value && callStatus.value === "") {
        st.value?.startST()
      }
    }, 800) // 增加延迟时间，确保TTS完全停止
  } catch (error) {
    console.error("打断通话失败:", error)
  }
}
```

### 2. 改进abort函数为异步
```javascript
const abort = async () => {
  try {
    // 发送停止请求
    request({
      url: "/mini/conversation_stop",
      method: "POST",
      hideLoading: true,
    })
    
    // 处理消息状态
    const lastMessage = messagesList.value[messagesList.value.length - 1]
    if (lastMessage) {
      if (lastMessage.status === "generating") {
        messagesList.value.pop()
      } else if (lastMessage.status === "output") {
        lastMessage.status = "done"
        lastMessage.voiceStatus = "inactive"
      }
    }

    // 清理语音队列和标志
    msgContentQueue = []
    speakFlag = false
    isGenerating.value = false
    
    // 确保TTS完全停止
    await tts.value?.stopTTS()
    
    store.getUserInfo()
  } catch (error) {
    console.error("中止生成失败:", error)
  }
}
```

### 3. 改进stopTTS函数
```javascript
const stopTTS = async (stopAudio = true) => {
  try {
    // 立即停止当前播放
    if (stopAudio && audioCtx) {
      audioCtx.stop()
      // 延迟设置为null，确保stop事件能正确触发
      setTimeout(() => {
        audioCtx = null
      }, 100)
    }
    
    // 清理队列
    queue = []
    fileQueue = []
    handleCount = 0
    allDoneFlag = false
    
    // 立即更新状态
    if (stopAudio) {
      voiceStatus = "stop"
      onVoiceStatusChangeCallback?.("stop")
    }
    
    // 关闭TTS连接
    if (tts) {
      await tts.close()
      tts = null
    }
    
    // 清理文件（可选）
    if (stopAudio) {
      await cleanupAllFiles()
    }
    
    // 最终重置状态
    voiceStatus = ""
  } catch (e) {
    console.error("停止TTS失败:", e)
  }
}
```

## 修复效果

### 修复前的执行顺序：
1. 用户点击打断
2. `callAbort()` 被调用
3. `setCallStatus("")` - 清空状态
4. `abort()` - 异步停止生成（但不等待TTS停止）
5. `st.value?.stopST()` - 停止语音识别
6. 500ms后 `st.value?.startST()` - 重启语音识别
7. **问题**: TTS可能还在播放，但ST已经开始监听

### 修复后的执行顺序：
1. 用户点击打断
2. `callAbort()` 被调用
3. `setCallStatus("")` - 清空状态
4. `await tts.value?.stopTTS()` - **等待TTS完全停止**
5. `await abort()` - **等待生成完全停止**
6. `await st.value?.stopST()` - 停止语音识别
7. 800ms后检查状态，如果仍在通话中才重启ST
8. **效果**: 确保TTS完全停止后才开始监听

## 测试步骤

1. **启动通话**: 点击通话按钮进入通话模式
2. **发起对话**: 说一句话让AI开始回复
3. **等待朗读**: 等AI开始朗读回复内容
4. **立即打断**: 在朗读过程中点击"点击打断"按钮
5. **验证效果**: 
   - 朗读应该立即停止
   - 界面应该显示"正在听"状态
   - 不应该出现朗读和监听同时进行的情况

## 预期改进

1. **即时响应**: 点击打断后朗读立即停止
2. **状态一致**: 不会出现"朗读没停止就开始听"的情况
3. **用户体验**: 打断功能更加可靠和直观
4. **资源管理**: 更好的异步操作管理，避免资源冲突

## 注意事项

- 延迟时间从500ms增加到800ms，确保所有异步操作完成
- 所有相关的abort调用都改为await，保证执行顺序
- TTS停止时立即更新状态，给用户即时反馈

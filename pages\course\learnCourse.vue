<script setup>
import { ref } from "vue"
import { onLoad } from "@dcloudio/uni-app"

const courseType = ref("")

const back = () => {
  uni.navigateBack()
}
const goLesson = (lessonType) => {
  uni.redirectTo({
    url: `/pages/course/learnCourse?lessonType=${lessonType}`,
  })
}

onLoad((options) => {
  courseType.value = options.lessonType || "video"
})
</script>

<template>
  <view>
    <image
      v-if="courseType === 'video'"
      mode="widthFix"
      src="https://yuexian-llm-1252495372.cos.ap-nanjing.myqcloud.com/weapp/04-3%E8%A7%86%E9%A2%91%E8%AF%BE%E7%A8%8B.jpg"
    />
    <image
      v-if="courseType === 'audio'"
      mode="widthFix"
      src="https://yuexian-llm-1252495372.cos.ap-nanjing.myqcloud.com/weapp/04-4%E9%9F%B3%E9%A2%91%E8%AF%BE%E7%A8%8B.jpg"
    />
    <image
      v-if="courseType === 'text'"
      mode="widthFix"
      src="https://yuexian-llm-1252495372.cos.ap-nanjing.myqcloud.com/weapp/04-5%E6%96%87%E6%9C%AC.jpg"
    />

    <view class="cover back" @click="back"></view>
    <view class="cover lesson lesson1" @click="goLesson('video')"></view>
    <view class="cover lesson lesson2" @click="goLesson('audio')"></view>
    <view class="cover lesson lesson3" @click="goLesson('text')"></view>
    <view class="cover otherLessons" @click="goLesson('text')"></view>
  </view>
</template>

<style scoped>
image {
  width: 100%;
}

.cover {
  position: absolute;
}

.back {
  width: 48rpx;
  height: 48rpx;
  top: 104rpx;
  left: 32rpx;
}

.lesson {
  width: 602rpx;
  height: 100rpx;
}

.lesson1 {
  top: 712rpx;
  left: 104rpx;
}

.lesson2 {
  top: 832rpx;
  left: 104rpx;
}

.lesson3 {
  top: 952rpx;
  left: 104rpx;
}

.otherLessons {
  width: 602rpx;
  height: 548rpx;
  top: 1072rpx;
  left: 104rpx;
}
</style>

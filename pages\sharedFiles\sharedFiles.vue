<script setup>
import { ref } from "vue"
import { onLoad } from "@dcloudio/uni-app"
import request from "@/request"

const attachmentIds = ref("")
const attachmentList = ref([])

const loadData = async () => {
  const res = await request({
    url: "/mini/get_file",
    method: "get",
    data: {
      files_id: attachmentIds.value,
    },
  })
  attachmentList.value = [res.result]
}

const onAttachment = (item) => {
  uni.showLoading({
    title: "下载中...",
  })
  uni.downloadFile({
    url: item.full_path,
    success: (res) => {
      uni.openDocument({
        filePath: res.tempFilePath,
        fileType: item.ext,
        complete: () => {
          uni.hideLoading()
        },
      })
    },
    fail: (err) => {
      console.log("err", err)
      uni.showToast({
        title: "下载失败",
        icon: "none",
      })
      uni.hideLoading()
    },
  })
}

const goHome = () => {
  uni.reLaunch({
    url: "/pages/index/index",
  })
}

onLoad((options) => {
  attachmentIds.value = options.attachment_ids
  loadData()
})
</script>

<template>
  <navBar title="分享的文件" :showBack="false">
    <template #leftIcon>
      <image
        style="width: 48rpx; height: 48rpx"
        src="/static/images/menu.svg"
        class="back"
        @click="goHome"
      />
    </template>
  </navBar>
  <view class="gradient-bg"> </view>

  <view class="attachment-list">
    <view
      class="attachment-item"
      v-for="item in attachmentList"
      :key="item.id"
      @click="onAttachment(item)"
    >
      <image src="/static/images/document.svg" class="attachment-item-icon" />
      <view class="attachment-item-name">{{ item.filename }}</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding: 0 32rpx;
  padding-top: 220rpx;

  .attachment-item {
    display: flex;
    align-items: center;
    gap: 24rpx;

    height: 180rpx;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(255, 255, 255, 0.7) 100%
    );
    box-shadow: 0rpx 32rpx 48rpx 0rpx rgba(160, 163, 189, 0.1);
    border-radius: 24rpx 24rpx 24rpx 24rpx;
    border: 2rpx solid rgba(252, 252, 252, 0.5);
    margin-bottom: 24rpx;
    padding: 24rpx;

    .attachment-item-icon {
      width: 108rpx;
      height: 132rpx;
      flex-shrink: 0;
    }

    .attachment-item-name {
      flex: 1;
      font-size: 34rpx;
      color: #253a57;
      line-clamp: 2;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>

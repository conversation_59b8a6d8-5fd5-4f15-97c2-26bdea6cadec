<script setup>
import {
  ref,
  computed,
  watch,
  onMounted,
  getCurrentInstance,
  nextTick,
} from "vue"
import request from "/request"
import contentList from "./contentList.vue"
import { rpx2px } from "/utils"

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  isMute: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(["manual", "rate", "refresh", "speak"])

const onSpeak = (message, index, mode) => {
  console.log("onSpeak", message, index, mode)
  if (mode === "history") {
    historyList.value.forEach((item, i) => {
      if (i !== index) {
        item.voiceStatus = ""
      }
    })
    props.list.forEach((item, i) => {
      item.voiceStatus = ""
    })
  } else {
    historyList.value.forEach((item, i) => {
      item.voiceStatus = ""
    })
    props.list.forEach((item, i) => {
      if (i !== index) {
        item.voiceStatus = ""
      }
    })
  }

  emit("speak", message)
}

watch(
  () => props.list.length,
  (newVal) => {
    console.log("props.list.length", newVal)

    props.list.forEach((item, index) => {
      if (index < props.list.length - 1) {
        item.voiceStatus = ""
      }
    })
    historyList.value.forEach((item) => {
      item.voiceStatus = ""
    })
  }
)

const onRate = (message) => {
  console.log("onRate", message)
  emit("rate", message)
}
const onManual = (message) => {
  console.log("onManual", message)
  emit("manual")
}
const onRefresh = (message) => {
  console.log("onRefresh", message)
  emit("refresh")
}

const nodeTime = ref("")
const historyList = ref([])
const historyInit = ref(false)
const loadMoreHistory = () => {
  request({
    url: "/mini/conversation_history",
    method: "get",
    data: {
      page_size: 5,
      time: nodeTime.value,
    },
  }).then((res) => {
    if (res.result?.length) {
      res.result = res.result.map((item) => {
        item.record_id = item.id
        return item
      })
      nodeTime.value = res.result[res.result.length - 1].created_at
      historyList.value.push(...res.result)
    }
    if (!historyInit.value) {
      historyInit.value = true
    }
  })
}

const onScrollToUpper = () => {
  console.log("onScrollToUpper")
  loadMoreHistory()
}
const refresherTriggered = ref(false)
const onPull = () => {
  console.log("onPull")
  refresherTriggered.value = true

  nextTick(() => {
    setTimeout(() => {
      refresherTriggered.value = false
    }, 50)
  })
}

let instance = null
let query
const windowHeight = ref(0)
const contentHeight = ref(0)
const scrollTop = ref(0)
const helloMessageHeight = ref(rpx2px(640))
const bottomPanelHeight = ref(rpx2px(472))
const placeholderHeight = computed(() => {
  return windowHeight.value - helloMessageHeight.value
})
const getWindowHeight = () => {
  query
    .select(".messages")
    .boundingClientRect((rect) => {
      windowHeight.value = rect.height
    })
    .exec()
}
const updateScrollTop = (mode) => {
  query
    .select(".list-content")
    .boundingClientRect()
    .exec((e) => {
      if (e[0]) {
        const lastEvent = e[e.length - 1]

        const currentContentHeight = contentHeight.value
        contentHeight.value = lastEvent.height
        if (mode === "upper") {
          const diff = lastEvent.height - currentContentHeight
          scrollTop.value = diff - rpx2px(100)
        } else {
          scrollTop.value =
            lastEvent.height - windowHeight.value + bottomPanelHeight.value
        }
      }
    })
}
const scrollIntoBottom = (offset = 0) => {
  query
    .select(".list-content")
    .boundingClientRect()
    .exec((e) => {
      if (e[0]) {
        const lastEvent = e[e.length - 1]
        contentHeight.value = lastEvent.height
        scrollTop.value = lastEvent.height - helloMessageHeight.value + offset
      }
    })
}

const lastMessage = computed(() => {
  return props.list[props.list.length - 1]
})

const autoScrollFlag = ref(true)
const onScroll = (e) => {
  const { scrollTop, scrollHeight, deltaY } = e.detail
  const leftBottom = scrollHeight - windowHeight.value - scrollTop
  if (leftBottom > 300) {
    autoScrollFlag.value = false
  } else if (leftBottom < 200) {
    autoScrollFlag.value = true
  }
}
const onScrollToLower = () => {
  autoScrollFlag.value = true
}

watch(
  () => lastMessage.value,
  (newVal) => {
    if (!autoScrollFlag.value) {
      return
    }
    setTimeout(() => {
      updateScrollTop("lower")
    }, 250)
  },
  { deep: true }
)

watch(
  () => props.list.length,
  (newVal) => {
    if (newVal) {
      autoScrollFlag.value = true
    }
  }
)

watch(
  () => historyInit.value,
  (newVal, oldValue) => {
    if (!oldValue && newVal) {
      nextTick(() => {
        setTimeout(() => {
          scrollIntoBottom(-70)
        }, 250)
      })
    }
  },
  { immediate: true }
)

onMounted(() => {
  instance = getCurrentInstance()
  query = uni.createSelectorQuery().in(instance)

  loadMoreHistory()
  getWindowHeight()
})

defineExpose({
  scrollIntoBottom,
  updateScrollTop,
})
</script>

<template>
  <scroll-view
    class="messages"
    scroll-y
    :scroll-top="scrollTop"
    refresher-enabled
    refresher-default-style="none"
    :refresher-triggered="refresherTriggered"
    upper-threshold="200"
    @scroll="onScroll"
    @scrolltoupper="onScrollToUpper"
    @scrolltolower="onScrollToLower"
    @refresherrefresh="onPull"
  >
    <view class="list-content">
      <contentList
        :list="historyList"
        :isMute="isMute"
        mode="history"
        @manual="onManual"
        @rate="onRate"
        @refresh="onRefresh"
        @speak="onSpeak"
      />

      <helloMessage />

      <contentList
        :list="list"
        :isMute="isMute"
        @manual="onManual"
        @rate="onRate"
        @refresh="onRefresh"
        @speak="onSpeak"
      />
    </view>

    <view :style="{ height: `${placeholderHeight}px` }"></view>
  </scroll-view>
</template>

<style>
.messages {
  width: 100%;
  height: 100%;
}

.list-content {
  padding: 0 20rpx;
}
</style>

<script setup>
const back = () => {
  uni.navigateBack()
}

const goChapter = () => {
  uni.navigateTo({
    url: "/pages/course/chapter",
  })
}
</script>

<template>
  <view>
    <image
      mode="widthFix"
      src="https://yuexian-llm-1252495372.cos.ap-nanjing.myqcloud.com/weapp/05-1%E6%B3%95%E5%BE%8B%E4%B8%93%E9%A2%98--%E8%AF%BE%E7%A8%8B%E5%88%97%E8%A1%A81.jpg"
    />

    <view class="cover back" @click="back"></view>
    <view class="cover chapter1" @click="goChapter"></view>
    <view class="cover chapter2" @click="goChapter"></view>
  </view>
</template>

<style scoped>
image {
  width: 100%;
}

.cover {
  position: absolute;
}

.back {
  width: 48rpx;
  height: 48rpx;
  top: 104rpx;
  left: 32rpx;
}

.chapter1 {
  width: 642rpx;
  height: 172rpx;
  top: 616rpx;
  left: 62rpx;
}

.chapter2 {
  width: 642rpx;
  height: 172rpx;
  top: 820rpx;
  left: 62rpx;
}
</style>

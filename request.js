import { useStore } from "/store"

const header = {
  Accept: "application/json",
}

let reqCount = 0
let isLoggingIn = false

const request = (config) => {
  const store = useStore()

  if (!config.hideLoading) {
    reqCount++
    uni.showLoading({
      title: "加载中",
    })
  }

  const requestConfig = {
    ...config,
    url: `${store.baseUrl}/api${config.url}`,
  }

  return new Promise((resolve, reject) => {
    uni
      .request({
        ...requestConfig,
        header: {
          ...header,
          Authorization: `Bearer ${store.token}`,
        },
      })
      .then((res) => {
        if (!config.hideLoading) {
          reqCount--
          if (reqCount === 0) {
            uni.hideLoading({
              noConflict: true,
            })
          }
        }

        resolve(res.data)

        if (res.statusCode === 401) {
          if (isLoggingIn) return
          isLoggingIn = true
          store.login().then(() => {
            isLoggingIn = false
            uni.reLaunch({
              url: "/pages/index/index",
            })
          })
        } else if (res.data.code !== 200) {
          let msg = res.data?.error || res.data?.message || "未知错误"
          uni.showToast({
            title: msg,
            icon: "none",
          })
        }
      })
      .catch((err) => {
        if (!config.hideLoading) {
          reqCount--
          if (reqCount === 0) {
            uni.hideLoading({
              noConflict: true,
            })
          }
        }
        uni.showModal({
          title: "温馨提示",
          content: err.errMsg || JSON.stringify(err),
          showCancel: false,
        })
        reject(err)
      })
  })
}

export const fetchUpload = function (
  filePath,
  extra = {},
  originName,
  uploadUrl = "/mini/upload_file",
  onProgress
) {
  const store = useStore()

  const url = `${store.baseUrl}/api${uploadUrl}`
  const token = store.token || ""
  uni.showLoading({
    title: "上传中...",
  })
  const formData = {
    extra: JSON.stringify(extra),
  }

  if (originName) {
    formData.original_name = originName
  }

  return new Promise((resolve, reject) => {
    const uploadTask = uni.uploadFile({
      url: url,
      name: "file",
      filePath: filePath,
      formData,
      header: {
        Accept: "application/json",
        Authorization: token ? "Bearer " + token : "",
      },
      success: (res) => {
        uni.hideLoading({
          noConflict: true,
        })
        try {
          const data = typeof res.data === "string" ? JSON.parse(res.data) : res.data
          resolve(data)
        } catch (e) {
          resolve(res.data)
        }
      },
      fail: (err) => {
        uni.hideLoading({
          noConflict: true,
        })
        uni.showModal({
          title: "出错了",
          content: err.errMsg || JSON.stringify(err),
        })
        console.error(err)
        reject(err)
      },
    })

    if (typeof onProgress === "function" && uploadTask && uploadTask.onProgressUpdate) {
      uploadTask.onProgressUpdate((progressEvent) => {
        const progress = progressEvent.progress || 0
        const loaded = progressEvent.totalBytesSent
        const total = progressEvent.totalBytesExpectedToSend
        try {
          onProgress({ progress, loaded, total })
        } catch (_) {}
      })
    }
  })
}

export default request

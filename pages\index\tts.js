import SpeechSynthesizer from "/utils/tts"
import { sleep, getRandomString } from "/utils"
import { useStore } from "/store"

const store = useStore()
const fs = wx.getFileSystemManager()

let tts
let audioCtx
let queue = []
let handleCount = 0
let fileQueue = []
let allDoneFlag = false
let changeCallback, onVoiceStatusChangeCallback
let voiceStatus = ""

const handleFileQueue = async (fileQueue) => {
  const file = fileQueue.shift()
  try {
    await playFile(file)
    if (
      allDoneFlag &&
      fileQueue.indexOf(file) === fileQueue.length - 1 &&
      voiceStatus !== "complete"
    ) {
      voiceStatus = "complete"
      onVoiceStatusChangeCallback?.("complete")
    } else {
      handleFileQueue(fileQueue)
    }
  } catch (err) {
    console.error("播放失败:", err)
    // 播放失败时立即清理当前文件
    cleanupFile(file)
    if (fileQueue.length) {
      handleFileQueue(fileQueue)
    } else {
      voiceStatus = "error"
      onVoiceStatusChangeCallback?.("error")
    }
  }
}

const playFile = (file) => {
  return new Promise((resolve, reject) => {
    audioCtx = wx.createInnerAudioContext({
      useWebAudioImplement: true,
    })
    audioCtx.src = file
    audioCtx.autoplay = voiceStatus !== "pause"

    // 增加超时时间到10秒，避免正常音频被误判为超时
    const timeoutId = setTimeout(() => {
      console.warn("音频播放超时:", file)
      if (audioCtx) {
        audioCtx.destroy()
        audioCtx = null
      }
      cleanupFile(file)
      reject(new Error("播放超时"))
    }, 10000)

    const updateStatus = (status) => {
      voiceStatus = status
      onVoiceStatusChangeCallback?.(status)
    }

    const cleanup = () => {
      clearTimeout(timeoutId)
      if (audioCtx) {
        audioCtx.destroy()
        setTimeout(() => {
          audioCtx = null
        }, 100)
      }
    }

    audioCtx.onPlay(() => {
      clearTimeout(timeoutId)
      updateStatus("play")
    })

    audioCtx.onEnded(() => {
      cleanup()
      cleanupFile(file)
      resolve()
    })

    audioCtx.onError((res) => {
      console.error("播放失败", res)
      cleanup()
      cleanupFile(file)
      reject(res)
    })

    audioCtx.onStop(() => {
      updateStatus("stop")
      cleanup()
      cleanupFile(file)
      resolve()
    })

    audioCtx.onPause(() => {
      updateStatus("pause")
    })
  })
}

// 清理单个文件
const cleanupFile = (filePath) => {
  try {
    fs.unlink({
      filePath,
      fail: (err) => console.error("删除文件失败", err),
    })
  } catch (error) {
    console.error("清理文件失败", error)
  }
}

// 检查存储空间使用情况
const checkStorageSpace = async () => {
  try {
    const storageInfo = await new Promise((resolve) => {
      wx.getStorageInfo({
        success: (res) => resolve(res),
        fail: () => resolve({ currentSize: 0, limitSize: 10240 }) // 默认10MB
      })
    })
    
    const usagePercent = (storageInfo.currentSize / storageInfo.limitSize) * 100

    // 如果使用率超过80%，建议清理
    if (usagePercent > 80) {
      return true
    }
    return false
  } catch (error) {
    console.error("检查存储空间失败", error)
    return false
  }
}

// 清理所有TTS相关文件
const cleanupAllFiles = async () => {
  try {
    // 清理当前队列中的文件
    await Promise.all(
      fileQueue.map(
        (file) =>
          new Promise((resolve) => {
            fs.unlink({
              filePath: file,
              complete: resolve,
            })
          })
      )
    )
    fileQueue = []
    
    // 清理用户数据目录下的所有mp3文件
    const files = await new Promise((resolve) => {
      fs.readdir({
        dirPath: wx.env.USER_DATA_PATH,
        success: (res) => resolve(res.files || []),
        fail: () => resolve([])
      })
    })
    
    const mp3Files = files.filter(file => file.endsWith('.mp3'))
    await Promise.all(
      mp3Files.map(
        (file) =>
          new Promise((resolve) => {
            fs.unlink({
              filePath: `${wx.env.USER_DATA_PATH}/${file}`,
              complete: resolve,
            })
          })
      )
    )

    // 清理后再次检查存储空间
    await checkStorageSpace()
  } catch (error) {
    console.error("清理所有文件失败", error)
  }
}



const handleQueue = (_handleCount) => {
  const dataArr = queue[_handleCount]

  if (!dataArr?.length) {
    return
  }

  const filePath = `${wx.env.USER_DATA_PATH}/${getRandomString(32)}.mp3`

  fs.open({
    filePath,
    flag: "a+",
    success: (res) => {
      try {
        dataArr.forEach((data, index) => {
          try {
            fs.appendFileSync(filePath, data, "binary")
          } catch (appendError) {
            console.error("appendFileSync失败:", appendError)
            // 如果是存储空间不足错误，先清理再重试
            if (appendError.errMsg && appendError.errMsg.includes('maximum size')) {
              cleanupAllFiles().then(() => {
                try {
                  fs.appendFileSync(filePath, data, "binary")
                } catch (retryError) {
                  throw retryError
                }
              })
            } else {
              throw appendError
            }
          }

          if (index === dataArr.length - 1) {
            fs.close({
              fd: res.fd,
              success: () => {
                fileQueue.push(filePath)
                if (_handleCount === 0) {
                  handleFileQueue(fileQueue)
                  voiceStatus = "start"
                  onVoiceStatusChangeCallback?.("start")
                }
              },
              fail: (err) => {
                console.error("关闭文件失败:", err)
                // 关闭失败时也要清理文件
                cleanupFile(filePath)
              },
            })
          }
        })
      } catch (error) {
        console.error("写入文件失败:", error)
        // 写入失败时清理文件
        fs.close({
          fd: res.fd,
          complete: () => cleanupFile(filePath)
        })
      }
    },
    fail: (err) => {
      console.error("打开文件失败:", err)
    },
  })
}

const init = async () => {
  if (!store.nlsToken) {
    await store.fetchNlsToken()
  }

  tts = new SpeechSynthesizer({
    url: store.nlsUrl,
    appkey: store.nlsAppKey,
    token: store.nlsToken,
  })

  const setupEventHandler = (event, handler) => {
    tts.on(event, handler)
  }

  setupEventHandler("data", (msg) => {
    // console.log("Client recv data:", JSON.stringify(msg))
    if (!queue[queue.length - 1]) {
      queue[queue.length - 1] = []
    }
    queue[queue.length - 1].push(msg)
  })

  setupEventHandler("started", (msg) => {
    changeCallback?.("started", JSON.parse(msg))
  })

  setupEventHandler("completed", async (msg) => {
    allDoneFlag = true
    changeCallback?.("completed", JSON.parse(msg))
  })

  setupEventHandler("begin", (msg) => {
    queue.push([])
    changeCallback?.("begin", JSON.parse(msg))
  })

  setupEventHandler("end", (msg) => {
    changeCallback?.("end", JSON.parse(msg))
    handleQueue(handleCount)
    handleCount++
  })

  setupEventHandler("closed", () => {
    changeCallback?.("closed")
  })

  setupEventHandler("failed", (msg) => {
    allDoneFlag = true
    changeCallback?.("failed", JSON.parse(msg))
  })
}

export const useTTS = () => {
  init()

  const startTTS = async () => {
    if (!tts) {
      await init()
    }

    // 检查存储空间，如果使用率过高则清理
    const shouldCleanup = await checkStorageSpace()
    if (shouldCleanup) {
      await cleanupAllFiles()
    }

    queue = []
    handleCount = 0
    fileQueue = []
    allDoneFlag = false

    await tts.start(
      Object.assign(tts.defaultStartParams(), {
        voice: "zhiyuan",
        format: "mp3",
      })
    )
  }

  // 添加TTS启动重试机制
  const startTTSWithRetry = async (maxRetries = 3) => {
    let retryCount = 0

    while (retryCount <= maxRetries) {
      try {
        // 确保之前的实例被清理
        if (tts) {
          await stopTTS(false) // 不清理文件，只停止服务
          await sleep(200)
          tts = null
        }

        await init()
        await startTTS()
        console.log("TTS启动成功")
        return
      } catch (err) {
        console.error(`TTS启动失败，重试次数: ${retryCount}`, err)
        retryCount++
        if (retryCount > maxRetries) {
          const errorMsg = `TTS启动失败，已重试${maxRetries}次: ${err.message}`
          console.error(errorMsg)
          throw new Error(errorMsg)
        }
        // 指数退避重试
        const delay = Math.min(1000 * Math.pow(2, retryCount), 5000)
        await sleep(delay)
      }
    }
  }

  const sendTTSText = async (text) => {
    const cleanText = text.replace(/[#`*]/g, "")
    tts.send(cleanText)
  }

  const stopTTS = async (stopAudio = true) => {
    try {
      // 立即停止当前播放
      if (stopAudio && audioCtx) {
        audioCtx.stop()
        // 延迟设置为null，确保stop事件能正确触发
        setTimeout(() => {
          audioCtx = null
        }, 100)
      }

      // 清理队列
      queue = []
      fileQueue = []
      handleCount = 0
      allDoneFlag = false

      // 立即更新状态
      if (stopAudio) {
        voiceStatus = "stop"
        onVoiceStatusChangeCallback?.("stop")
      }

      // 关闭TTS连接
      if (tts) {
        await tts.close()
        tts = null
      }

      // 清理文件（可选）
      if (stopAudio) {
        await cleanupAllFiles()
      }

      // 最终重置状态
      voiceStatus = ""
    } catch (e) {
      console.error("停止TTS失败:", e)
    }
  }

  const onTTSChange = (callback) => {
    changeCallback = callback
  }

  const pauseTTS = () => {
    try {
      if (audioCtx && voiceStatus === "play") {
        audioCtx.pause()
        voiceStatus = "pause"
        onVoiceStatusChangeCallback?.("pause")
      }
    } catch (e) {
      console.error("暂停TTS失败:", e)
    }
  }

  const resumeTTS = () => {
    try {
      if (audioCtx && voiceStatus === "pause") {
        audioCtx.play()
        // 状态会在onPlay回调中更新
      }
    } catch (e) {
      console.error("恢复TTS失败:", e)
    }
  }

  const onVoiceStatusChange = (callback) => {
    onVoiceStatusChangeCallback = callback
  }

  const getVoiceStatus = () => voiceStatus

  return {
    startTTS: startTTSWithRetry, // 使用重试版本作为默认启动方法
    sendTTSText,
    stopTTS,
    pauseTTS,
    resumeTTS,
    onTTSChange,
    onVoiceStatusChange,
    getVoiceStatus,
    cleanupAllFiles, // 导出清理方法供外部调用
    checkStorageSpace, // 导出存储检查方法
  }
}

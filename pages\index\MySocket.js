import { useStore } from "/store"

export default class MySocket {
  constructor(config) {
    this.isConnecting = false
    this.socketTask = null
    this.store = useStore()
    this.config = config
    this.heartbeatTimer = null
  }

  async init() {
    // 先清理旧连接
    if (this.socketTask) {
      this.socketTask.close()
      this.socketTask = null
    }

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    // 防止重复初始化
    if (this.isConnecting) {
      return
    }

    const maxRetries = 3
    let retryCount = 0

    const connect = () => {
      return new Promise((resolve, reject) => {
        this.socketTask = uni.connectSocket({
          url: `${this.store.socketUrl}${
            this.store.token ? `?token=${this.store.token}` : ""
          }`,
          success: (res) => {
            console.log("WebSocket连接创建成功")
          },
          fail: (err) => {
            console.log("WebSocket连接创建失败", err)
            reject(err)
          },
        })

        this.socketTask.onOpen(() => {
          this.isConnecting = true
          console.log("WebSocket连接打开")
          this.login()
          this.socketTask.onMessage((res) => {
            const data = JSON.parse(res.data)
            if (data.result?.action === "login") {
              console.log("登录成功")
              resolve(res)
            }
            this.config.onMessage(data)
          })

          // 清除之前的心跳定时器
          if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer)
          }
          // 创建新的心跳定时器
          this.heartbeatTimer = setInterval(() => {
            this.send({ type: "ping" })
          }, 10000)
        })

        this.socketTask.onClose(() => {
          console.log("WebSocket连接关闭")
          this.isConnecting = false
          this.init()
        })

        this.socketTask.onError((err) => {
          console.log("WebSocket连接错误", err)
        })
      })
    }

    const tryConnect = async () => {
      try {
        await connect()
      } catch (err) {
        if (retryCount < maxRetries) {
          retryCount++
          this.reconnectTimer = setTimeout(tryConnect, 1000 * retryCount)
        }
      }
    }

    return tryConnect()
  }

  login() {
    this.socketTask.send({
      data: JSON.stringify({
        action: "login",
        token: this.store.token,
        role: "user",
      }),
      success: (res) => {
        console.log("发送成功")
      },
      fail: (err) => {
        console.error("发送失败", err)
      },
    })
  }

  send(data) {
    console.log("发送内容：" + JSON.stringify(data))
    return new Promise((resolve, reject) => {
      this.socketTask.send({
        data: JSON.stringify(data),
        success: (res) => {
          console.log("发送成功")
          resolve(res)
        },
        fail: (err) => {
          console.log("发送失败")
          this.isConnecting = false
          this.init()
          reject(err)
        },
      })
    })
  }

  close() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
    return new Promise((resolve, reject) => {
      this.socketTask.close({
        success: (res) => {
          console.log("关闭成功")
          resolve(res)
        },
        fail: (err) => {
          console.log("关闭失败")
          reject(err)
        },
      })
    })
  }
}

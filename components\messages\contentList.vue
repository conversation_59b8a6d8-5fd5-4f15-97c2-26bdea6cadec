<script setup>
import MessageContent from "./messageContent.vue"

import iconDocx from "@/static/images/file-icon-docx.svg"
import iconPdf from "@/static/images/file-icon-pdf.svg"

const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  isMute: {
    type: Boolean,
    default: false,
  },
  mode: {
    type: String,
    default: "chat",
  },
})

const emit = defineEmits(["manual", "rate", "refresh", "speak"])

const onCopy = (message) => {
  const text = message.content
  uni.setClipboardData({
    data: text,
    success: () => {
      uni.showToast({
        title: "已复制到剪贴板",
        icon: "none",
      })
    },
  })
}
const onSpeak = (message, index) => {
  emit("speak", message, index, props.mode)
}
const onRate = (message) => {
  emit("rate", message)
}
const onManual = (message) => {
  emit("manual")
}
const onRefresh = (message) => {
  emit("refresh")
}
</script>

<template>
  <view class="content-list" :class="{ reverse: mode === 'history' }">
    <view
      class="message"
      :class="[message.role]"
      :id="`message-${index}`"
      v-for="(message, index) in list"
      :key="message.id"
    >
      <template v-if="message.role === 'user'">
        <view
          class="message-content"
          :class="message.role"
          v-if="message.content"
        >
          {{ message.content }}
        </view>
        <view class="req-attachment" v-if="message.req_attachment">
          <image
            :src="iconDocx"
            v-if="message.req_attachment.ext === 'docx'"
            class="attachments-uploader-item-icon"
          />
          <image
            :src="iconPdf"
            v-else-if="message.req_attachment.ext === 'pdf'"
            class="attachments-uploader-item-icon"
          />

          <view class="attachments-uploader-item-name">
            {{ message.req_attachment.filename }}
          </view>
        </view>
      </template>

      <template v-else-if="['assistant', 'artificial'].includes(message.role)">
        <view class="generating" v-if="message.status === 'generating'">
          <image
            class="generating"
            src="/static/images/generating.gif"
            mode="aspectFit"
          />
          <text>思考中...</text>
        </view>

        <MessageContent
          style="overflow: hidden"
          v-else
          :message="message"
          :mode="mode"
          :is-last="index === list.length - 1"
          @copy="() => onCopy(message)"
          @speak="() => onSpeak(message, index)"
          @rate="() => onRate(message)"
          @manual="() => onManual(message)"
          @refresh="() => onRefresh(message)"
        />
      </template>

      <view class="manual" v-else-if="message.role === 'manual'">
        <template v-if="message.status === 'entered'"
          >您已成功接入人工客服
        </template>
        <template v-else-if="message.status === 'exited'"
          >您已退出人工客服
        </template>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.content-list {
  display: flex;
  flex-direction: column;

  &.reverse {
    flex-direction: column-reverse;
  }
}

.message {
  padding: 12rpx 0;
  display: flex;

  &.user {
    padding-left: 42rpx;
    flex-direction: column;
    align-items: flex-end;
    gap: 24rpx;
  }
}

.message-content {
  font-size: 34rpx;
  line-height: 54rpx;
  padding: 30rpx;
  text-align: justify;
  word-break: break-all;

  &.user {
    background: #466fff;
    border-radius: 48rpx 48rpx 12rpx 48rpx;
    color: #ffffff;
    width: fit-content;
    max-width: 100%;
  }
}

.manual {
  width: 100%;
  font-size: 24rpx;
  padding: 20rpx;
  text-align: center;
  color: #6e7191;
}

.generating {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  font-weight: 500;
  color: #6e7191;

  image {
    width: 102rpx;
    height: 102rpx;
    margin-right: 24rpx;
  }
}

.req-attachment {
  width: 540rpx;
  height: 114rpx;
  background: rgba(217, 217, 217, 0);
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  border: 2rpx solid rgba(70, 111, 255, 0.31);
  padding: 14rpx 20rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 30rpx;

  .attachments-uploader-item-icon {
    width: 84rpx;
    height: 84rpx;
    flex-shrink: 0;
  }

  .attachments-uploader-item-name {
    flex: 1;
    font-size: 20rpx;
    color: #253a57;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>

<script setup>
const back = () => {
  uni.navigateBack()
}

const goLesson = (lessonType) => {
  uni.navigateTo({
    url: `/pages/course/learnCourse?lessonType=${lessonType}`,
  })
}
</script>

<template>
  <view>
    <image
      mode="widthFix"
      src="https://yuexian-llm-1252495372.cos.ap-nanjing.myqcloud.com/weapp/05-2%E7%AB%A0%E8%8A%82%E5%88%97%E8%A1%A81.jpg"
    />

    <view class="cover back" @click="back"></view>
    <view class="cover lesson lesson1" @click="goLesson('video')"></view>
    <view class="cover lesson lesson2" @click="goLesson('audio')"></view>
    <view class="cover lesson lesson3" @click="goLesson('text')"></view>
    <view class="cover otherLessons" @click="goLesson('text')"></view>
  </view>
</template>

<style scoped>
image {
  width: 100%;
}

.cover {
  position: absolute;
}

.back {
  width: 48rpx;
  height: 48rpx;
  top: 104rpx;
  left: 32rpx;
}

.lesson {
  width: 602rpx;
  height: 100rpx;
}

.lesson1 {
  top: 690rpx;
  left: 104rpx;
}

.lesson2 {
  top: 810rpx;
  left: 104rpx;
}

.lesson3 {
  top: 930rpx;
  left: 104rpx;
}

.otherLessons {
  width: 602rpx;
  height: 548rpx;
  top: 1050rpx;
  left: 104rpx;
}
</style>

<script setup>
import { ref, watch, computed, provide, nextTick, onUnmounted } from "vue"
import {
  onLoad,
  onShow,
  onShareAppMessage,
  onShareTimeline,
} from "@dcloudio/uni-app"
import { sleep } from "/utils"
import { useStore } from "/store"
import request from "/request"
import MySocket from "./MySocket"
import { useTTS } from "./tts"
import { useST } from "./st"

const store = useStore()

// 语音相关状态
const tts = ref(null)
const st = ref(null)
const isMute = ref(false)

// 消息相关状态
const isGenerating = ref(false)
const messagesList = ref([])
const chatMode = ref("assistant")

// 语音队列相关
let msgContentQueue = []
let speakFlag = false

// 弹窗状态
const rechargePopupVisible = ref(false)
const feedBackVisible = ref(false)
const serviceVisible = ref(false)
const completeInfoPopupVisible = ref(false)
const callVisible = ref(false)
const callStatus = ref("")

// 评价相关
const toRateAnswerInstance = ref(null)
const defaultRateAnswer = ref(null)

// 计算属性
const headerHeight = computed(() => store.headerHeight)
const leftTimes = computed(() => store.leftTimes)

// 消息处理
const handleSpeakRealTime = async () => {
  try {
    if (msgContentQueue.length === 0) {
      const lastMessage = messagesList.value[messagesList.value.length - 1]
      if (lastMessage?.status !== "done") {
        await sleep(300)
        handleSpeakRealTime()
      } else {
        msgContentQueue = []
        speakFlag = false
        tts.value?.stopTTS(false)
      }
      return
    }
    const text = msgContentQueue.shift()
    if (text && text.trim()) {
      await tts.value?.sendTTSText(text)
      await sleep(100)
    }
    handleSpeakRealTime()
  } catch (error) {
    console.error("Error in handleSpeakRealTime:", error)
  }
}

// 语音监听
const listenTTS = () => {
  tts.value?.onTTSChange(async (name) => {
    if (name === "failed" && callVisible.value) {
      setCallStatus("")
      await st.value?.stopST()
      // 延迟重启，避免资源冲突
      setTimeout(() => {
        if (callVisible.value) {
          st.value?.startST()
        }
      }, 500)
    }
  })
}

const listenSt = () => {
  st.value?.onStChange(async (name, msg) => {
    switch (name) {
      case "started":
        setCallStatus("listen")
        uni.hideLoading({ noConflict: true })
        break

      case "end":
        await st.value?.stopST()
        const text = msg.payload.result
        if (text) {
          onConfirm(text)
            .then(() => {
              setCallStatus("think")
            })
            .catch(() => {
              setTimeout(() => {
                if (callVisible.value && callStatus.value !== "speak") {
                  st.value?.startST()
                }
              }, 1000)
            })
        }
        break

      case "failed":
        // 避免重复启动ST，添加状态检查
        if (callStatus.value === "listen" && callVisible.value) {
          setCallStatus("")
          await st.value?.stopST()
          // 延迟重启，避免立即重试导致的资源冲突
          setTimeout(() => {
            if (callVisible.value && callStatus.value === "") {
              st.value?.startST()
            }
          }, 500)
        }
        break
    }
  })
}

// Socket消息处理
const onMessage = async (res) => {
  try {
    const { action, data, content_type } = res.result

    if (action === "reply") {
      if (data.session_id) {
        store.setSessionId(data.session_id)
      }

      const lastMessage = messagesList.value[messagesList.value.length - 1]

      if (chatMode.value === "artificial") {
        messagesList.value.push({
          role: "artificial",
          content: data.content,
          status: "done",
        })
      } else if (chatMode.value === "assistant") {
        if (lastMessage.status === "done") {
          return
        }
        lastMessage.status = "output"
        if (content_type === "text") {
          lastMessage.content += data.content
        } else if (content_type === "waiting_for_file") {
          lastMessage.res_attachment = {
            id: "waiting_for_file",
            filename: "正在生成文件...",
            ext: "docx",
            full_path: "",
            status: "waiting_for_file",
          }
        } else if (content_type === "file") {
          const attachment_id = data.content
          const attachment = await request({
            url: `/mini/get_file?files_id=${attachment_id}`,
            method: "GET",
          })
          if (attachment.code === 200) {
            lastMessage.res_attachment = attachment.result
          }
        }
      }

      if (callVisible.value && data.content.trim()) {
        msgContentQueue.push(data.content)
        if (!speakFlag) {
          speakFlag = true
          tts.value?.startTTS()
          await sleep(500)
          handleSpeakRealTime()
        }
      }

      if (data.finish_reason === "stop") {
        store.minusLeftTimes()
        isGenerating.value = false
        lastMessage.status = "done"
        lastMessage.record_id = data.record_id
      }
    }
  } catch (error) {
    console.error("Error in onMessage:", error)
  }
}

const mySocket = new MySocket({ onMessage })

// 消息发送
const onConfirm = (text, ability, attachment = null) => {
  console.log("onConfirm", text, ability, attachment)

  if (isGenerating.value) {
    abort()
  } else {
    tts.value?.stopTTS()
  }

  return new Promise((resolve, reject) => {
    if (!mySocket?.isConnecting) {
      reject("socket is not connecting")
    }

    const data = {
      action: "question",
      prompt: text,
    }
    if (ability) {
      data.ability = ability
    }
    if (attachment) {
      data.attachment_id = attachment.id
    }

    mySocket
      .send(data)
      .then(() => {
        resolve()
        messagesList.value.push({
          role: "user",
          content: text,
          req_attachment: attachment,
        })

        if (chatMode.value === "assistant") {
          messagesList.value.push({
            role: "assistant",
            content: "",
            status: "generating",
          })
          isGenerating.value = true

          if (callVisible.value) {
            const msg = messagesList.value[messagesList.value.length - 1]
            msg.voiceStatus = "loading"

            tts.value?.onVoiceStatusChange((e) => {
              handleVoiceStatusChange(e, msg)
            })
          }
        }

        msgContentQueue = []
        speakFlag = false
      })
      .catch(async (err) => {
        reject(err)
        console.error("onConfirm", err)
        uni.showToast({
          title: "发送失败, 请重试",
          icon: "none",
        })
        if (callVisible.value) {
          setCallStatus("")
          await st.value?.stopST()
          // 延迟重启，避免资源冲突
          setTimeout(() => {
            if (callVisible.value) {
              st.value?.startST()
            }
          }, 500)
        }
      })
  })
}

// 语音状态变化处理
const handleVoiceStatusChange = (status, msg) => {
  switch (status) {
    case "play":
      msg.voiceStatus = "active"
      if (callVisible.value) {
        setCallStatus("speak")
      }
      break
    case "pause":
    case "stop":
      msg.voiceStatus = "inactive"
      break
    case "complete":
    case "error":
      msg.voiceStatus = "inactive"
      if (callVisible.value) {
        // 语音播放完成后重新启动语音识别
        setTimeout(() => {
          if (callVisible.value && callStatus.value !== "pause") {
            st.value?.startST()
          }
        }, 300)
      }
      break
    default:
      break
  }
}

// 通话状态管理
const setCallStatus = (status) => {
  console.log("设置通话状态:", callStatus.value, "->", status)
  callStatus.value = status
}

// 通话相关
const showCall = async () => {
  try {
    if (isGenerating.value) {
      abort()
    } else {
      await tts.value?.stopTTS()
    }

    uni.showLoading({
      title: "连接中...",
      mask: true,
    })

    callVisible.value = true
    setCallStatus("")
    listenSt()
    await st.value?.startST()
    wx.setKeepScreenOn({
      keepScreenOn: true,
    })
  } catch (error) {
    console.error("启动通话失败:", error)
    uni.hideLoading()
    callVisible.value = false
    uni.showToast({
      title: "通话启动失败",
      icon: "none",
    })
  }
}

const exitCall = async () => {
  try {
    callVisible.value = false
    setCallStatus("")
    wx.setKeepScreenOn({
      keepScreenOn: false,
    })
    await st.value?.stopST()
    await tts.value?.stopTTS()
    // 清理语音队列
    msgContentQueue = []
    speakFlag = false
  } catch (error) {
    console.error("退出通话失败:", error)
  }
}

let callStatusBeforePause = ""

const callPause = async () => {
  try {
    callStatusBeforePause = callStatus.value
    if (callStatus.value === "speak") {
      await tts.value?.pauseTTS()
    } else {
      abort()
    }
    await st.value?.stopST()
    setCallStatus("pause")
  } catch (error) {
    console.error("暂停通话失败:", error)
  }
}

const callResume = async () => {
  try {
    if (callStatusBeforePause === "speak") {
      await tts.value?.resumeTTS()
    } else {
      await st.value?.startST()
    }
    setCallStatus(callStatusBeforePause)
    callStatusBeforePause = ""
  } catch (error) {
    console.error("恢复通话失败:", error)
    // 恢复失败时重新启动ST
    await st.value?.startST()
  }
}

const callAbort = async () => {
  try {
    setCallStatus("")
    abort()
    await st.value?.stopST()
    // 延迟重启，避免资源冲突
    setTimeout(() => {
      if (callVisible.value) {
        st.value?.startST()
      }
    }, 500)
  } catch (error) {
    console.error("打断通话失败:", error)
  }
}

// 评价相关
const toRateAnswer = async (message) => {
  toRateAnswerInstance.value = message
  try {
    const res = await request({
      url: `/mini/conversation_record/${message.record_id}/assessment`,
      method: "GET",
    })
    if (res?.code === 200) {
      defaultRateAnswer.value = res.result
    }
    feedBackVisible.value = true
  } catch (error) {
    console.error("Error in toRateAnswer:", error)
  }
}

const onConfirmRate = async (data) => {
  try {
    const res = await request({
      url: `/mini/conversation_record/${toRateAnswerInstance.value.record_id}/assessment`,
      method: "POST",
      data,
    })
    if (res.code === 200) {
      feedBackVisible.value = false
      defaultRateAnswer.value = null
      uni.showToast({
        title: "评价成功",
        icon: "success",
      })
    }
  } catch (error) {
    console.error("Error in onConfirmRate:", error)
  }
}

// 中止生成
const abort = () => {
  request({
    url: "/mini/conversation_stop",
    method: "POST",
    hideLoading: true,
  })
  const lastMessage = messagesList.value[messagesList.value.length - 1]
  if (lastMessage) {
    if (lastMessage.status === "generating") {
      messagesList.value.pop()
    } else if (lastMessage.status === "output") {
      lastMessage.status = "done"
      lastMessage.voiceStatus = "inactive"
    }
  }

  msgContentQueue = []
  speakFlag = false
  tts.value?.stopTTS()
  isGenerating.value = false
  store.getUserInfo()
}

// 重新生成和重新播放
const reGenerate = () => {
  tts.value?.stopTTS()
  messagesList.value.pop()
  const lastMessage = messagesList.value[messagesList.value.length - 1]
  const text = lastMessage.content
  messagesList.value.pop()
  onConfirm(text)
}

const reSpeak = async (message) => {
  if (message.voiceStatus === "active") {
    tts.value?.stopTTS()
    message.voiceStatus = "inactive"
  } else if (!message.voiceStatus || message.voiceStatus === "inactive") {
    message.voiceStatus = "loading"
    await tts.value?.stopTTS()
    tts.value?.startTTS()
    await sleep(1000)
    await tts.value?.sendTTSText(message.content)

    tts.value?.onVoiceStatusChange((e) => {
      switch (e) {
        case "start":
        case "play":
          message.voiceStatus = "active"
          break
        case "pause":
        case "stop":
        case "complete":
        case "error":
          message.voiceStatus = "inactive"
          break
      }
    })
  }
}

// 聊天模式切换
const toChangeChatMode = (mode) => {
  if (mode === chatMode.value) return

  if (mode === "artificial") {
    serviceVisible.value = true
  } else {
    uni.showModal({
      title: "提示",
      content: "是否退出人工服务？",
      success: async (res) => {
        if (res.confirm) {
          try {
            const res1 = await request({
              url: `/mini/conversation_setting`,
              method: "POST",
              data: {
                mode: "assistant",
                type: "consult",
              },
            })
            if (res1.code === 200) {
              chatMode.value = "assistant"
              messagesList.value.push({
                role: "manual",
                content: "",
                status: "exited",
              })
            }
          } catch (error) {
            console.error("Error in toChangeChatMode:", error)
          }
        }
      },
    })
  }
}

const confirmService = async () => {
  try {
    const res = await request({
      url: `/mini/conversation_setting`,
      method: "POST",
      data: {
        mode: "artificial",
        type: "consult",
      },
    })
    if (res.code === 200) {
      chatMode.value = "artificial"
      serviceVisible.value = false
      messagesList.value.push({
        role: "manual",
        content: "",
        status: "entered",
      })
    }
  } catch (error) {
    console.error("Error in confirmService:", error)
  }
}

// 其他功能
const toggleMute = () => {
  isMute.value = !isMute.value
  if (isMute.value) {
    tts.value?.stopTTS()
  }
}

const messageRef = ref(null)
const onInputFocus = () => {
  messageRef.value.scrollIntoBottom()
}
const onInputBlur = () => {
  messageRef.value.updateScrollTop("lower")
}

const checkAvailability = () => {
  if (store.isLogin && !store.isCompleteUserInfo) {
    completeInfoPopupVisible.value = true
  } else if (leftTimes.value <= 0) {
    if (!store.paySwitch) return
    rechargePopupVisible.value = true
  }
}

const goCompleteInfo = () => {
  uni.navigateTo({
    url: "/pages/profile/profile",
  })
  completeInfoPopupVisible.value = false
}

// 初始化
const init = async () => {
  try {
    mySocket.init()
    const res = await request({
      url: `/mini/conversation_setting`,
      method: "POST",
      data: {
        mode: "assistant",
        type: "consult",
      },
    })
    if (res.code === 200) {
      chatMode.value = "assistant"
    }
    await store.fetchNlsToken()
    tts.value = useTTS()
    st.value = useST()
    listenTTS()
    
    // 应用启动时检查并清理存储空间
    if (tts.value?.checkStorageSpace) {
      const shouldCleanup = await tts.value.checkStorageSpace()
      if (shouldCleanup && tts.value?.cleanupAllFiles) {
        await tts.value.cleanupAllFiles()
      }
    }
  } catch (error) {
    console.error("Error in init:", error)
  }
}

// 生命周期
watch(
  () => store.isLogin,
  (value) => {
    if (value) {
      init()
    }
  },
  { immediate: true }
)

onLoad(() => {
  // 页面加载
})

onShow(() => {
  // 页面显示
})

provide("st", st)

// 分享
onShareAppMessage(() => {
  return {
    title: store.inviteShareTitle,
    imageUrl: store.inviteShareImage,
    path: `/pages/index/index`,
  }
})

onShareTimeline(() => {})

onUnmounted(() => {
  // 清理语音识别资源
  st.value?.cleanup?.()
  // 停止语音合成
  tts.value?.stopTTS()
  // 关闭 socket 连接
  mySocket?.close()
})
</script>

<template>
  <image src="/static/images/index-bg.png" class="index-bg" />
  <indexHeader
    :leftTimes="leftTimes"
    :chatMode="chatMode"
    :isMute="isMute"
    @toggleMute="toggleMute"
  />
  <bottomPanel
    :chatMode="chatMode"
    :isGenerating="isGenerating"
    :isMute="isMute"
    :disabled="!store.accountAvailability"
    @toggleMute="toggleMute"
    @confirm="onConfirm"
    @exitArtificial="toChangeChatMode('assistant')"
    @call="showCall"
    @abort="abort"
    @inputFocus="onInputFocus"
    @inputBlur="onInputBlur"
    @click="checkAvailability"
  />

  <view class="index-content" @click="onContentClick">
    <messages
      ref="messageRef"
      v-if="store.isLogin"
      :list="messagesList"
      :isMute="isMute"
      @manual="toChangeChatMode('artificial')"
      @rate="toRateAnswer"
      @refresh="reGenerate"
      @speak="reSpeak"
    />
  </view>

  <call
    :visible="callVisible"
    :callStatus="callStatus"
    @pause="callPause"
    @resume="callResume"
    @abort="callAbort"
    @exit="exitCall"
  />

  <rechargePopup v-model:visible="rechargePopupVisible" />
  <feedBackPopup
    v-model:visible="feedBackVisible"
    :defaultData="defaultRateAnswer"
    @confirm="onConfirmRate"
  />
  <servicePopup v-model:visible="serviceVisible" @confirm="confirmService" />

  <completeInfoPopup
    v-model:visible="completeInfoPopupVisible"
    @confirm="goCompleteInfo"
  />

  <!-- <privacyPopup /> -->
</template>

<style lang="scss" scoped>
.index-bg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
}

.index-content {
  position: absolute;
  top: v-bind(headerHeight);
  left: 0;
  z-index: 0;
  width: 100vw;
  height: calc(100vh - v-bind(headerHeight));
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
  overflow: hidden;
}
</style>

<script setup>
const props = defineProps({
  title: String,
  showBack: {
    type: Boolean,
    default: true,
  },
  bgColor: {
    type: String,
    default: "none",
  },
})

const { statusBarHeight } = uni.getWindowInfo()

const back = () => {
  uni.navigateBack()
}
</script>

<template>
  <view
    class="nav-bar"
    :style="{ paddingTop: `${statusBarHeight}px`, backgroundColor: bgColor }"
  >
    <view class="content">
      <!-- 左侧图标区域 - 支持自定义 slot -->
      <view class="left-icon">
        <slot name="leftIcon">
          <image
            v-if="showBack"
            class="back"
            src="/static/images/back.svg"
            @click="back"
          />
        </slot>
      </view>
      <view class="title">
        {{ title }}
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.nav-bar {
  width: 100%;
  position: fixed;
  z-index: 999;

  .content {
    height: 88rpx;
    padding: 24rpx 32rpx;
    display: flex;
    align-items: center;
    position: relative;

    .left-icon {
      position: absolute;
      left: 24rpx;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .back {
      width: 48rpx;
      height: 48rpx;
    }

    .title {
      font-size: 32rpx;
      font-weight: 500;
      color: #253a57;
      width: 100%;
      text-align: center;
      padding: 0 160rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>

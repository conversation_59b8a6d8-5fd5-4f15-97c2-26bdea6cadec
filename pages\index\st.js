import SpeechTranscription from "/utils/st"
import { sleep } from "/utils"
import { useStore } from "/store"

const store = useStore()

let recordManager = wx.getRecorderManager()
let st

let changeCallback

const init = async () => {
  if (!store.nlsToken) {
    await store.fetchNlsToken()
  }

  recordManager.onFrameRecorded((res) => {
    if (res.frameBuffer) {
      st?.sendAudio(res.frameBuffer)
    }
  })
  recordManager.onStart(() => {
    // 开始录音
  })
  recordManager.onStop((res) => {
    if (res.tempFilePath) {
      wx.getFileSystemManager().removeSavedFile({
        filePath: res.tempFilePath,
      })
    }
  })
  recordManager.onError((res) => {
    // 录音失败
  })

  st = new SpeechTranscription({
    url: store.nlsUrl,
    appkey: store.nlsAppKey,
    token: store.nlsToken,
  })

  st.on("started", (msg) => {
    changeCallback && changeCallback("started", JSON.parse(msg))
  })

  st.on("changed", (msg) => {
    changeCallback && changeCallback("changed", JSON.parse(msg))
  })

  st.on("completed", (msg) => {
    changeCallback && changeCallback("completed", JSON.parse(msg))
  })

  st.on("begin", (msg) => {
    changeCallback && changeCallback("begin", JSON.parse(msg))
  })

  st.on("end", (msg) => {
    changeCallback && changeCallback("end", JSON.parse(msg))
  })

  st.on("closed", () => {
    recordManager.stop()
    changeCallback && changeCallback("closed")
  })

  st.on("failed", (msg) => {
    changeCallback && changeCallback("failed", msg)
  })
}

export const useST = () => {
  init()

  const startST = async () => {
    if (!st) {
      await init()
    }
    await st.start(
      Object.assign(st.defaultStartParams(), {
        format: "PCM",
        sample_rate: 8000,
        // max_sentence_silence: 1200, //语音断句检测阈值
        enable_intermediate_result: false, //是否返回中间识别结果
        enable_semantic_sentence_detection: true, //是否开启语义断句
      })
    )

    recordManager.start({
      duration: 600000,
      numberOfChannels: 1,
      sampleRate: 8000,
      format: "PCM",
      frameSize: 4,
    })
  }

  const stopST = () => {
    return new Promise(async (resolve, reject) => {
      try {
        // 停止录音
        if (recordManager) {
          recordManager.stop()
        }

        // 等待录音完全停止
        await sleep(200)

        // 关闭ST连接
        if (st) {
          await st.close()
        }
      } catch (e) {
        console.error("停止ST失败:", e)
      } finally {
        resolve()
      }
    })
  }

  const onStChange = (callback) => {
    changeCallback = callback
  }

  // 添加资源释放
  const cleanup = () => {
    if (recordManager) {
      recordManager.stop()
    }
    if (st) {
      st.shutdown()
      st = null
    }
    changeCallback = null
  }

  // 添加错误重试
  const startSTWithRetry = async (maxRetries = 3) => {
    let retryCount = 0

    while (retryCount <= maxRetries) {
      try {
        // 确保之前的实例被清理
        if (st) {
          await stopST()
          await sleep(200) // 等待清理完成
          st = null
        }

        // 重新初始化
        await init()
        await startST()
        console.log("语音识别启动成功")
        return
      } catch (err) {
        console.error(`语音识别启动失败，重试次数: ${retryCount}`, err)
        retryCount++
        if (retryCount > maxRetries) {
          // 最终失败，抛出错误
          const errorMsg = `语音识别启动失败，已重试${maxRetries}次: ${err.message}`
          console.error(errorMsg)
          throw new Error(errorMsg)
        }
        // 指数退避重试
        const delay = Math.min(1000 * Math.pow(2, retryCount), 5000)
        await sleep(delay)
      }
    }
  }

  return {
    startST: startSTWithRetry,
    stopST,
    onStChange,
    cleanup // 导出清理方法
  }
}

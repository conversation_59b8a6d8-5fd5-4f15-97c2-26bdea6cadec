<script>
import { useStore } from "/store.js"
import { rpx2px } from "/utils"

export default {
  onLaunch: function (options) {
    // 获取启动参数
    if (options.query.scene) {
      const scene = decodeURIComponent(options.query.scene)
      const params = scene.split("&")
      const query = {}
      params.forEach((item) => {
        const arr = item.split("=")
        query[arr[0]] = arr[1]
      })
      options.query = query
    }

    const store = useStore()
    store.getConfig()
    const token = uni.getStorageSync("token")
    if (token) {
      store.setToken(token)
      store.getUserInfo()
    } else {
      store.login(options.query.invitation_code)
    }
    const sessionId = uni.getStorageSync("sessionId")
    if (sessionId) {
      store.setSessionId(sessionId)
    }

    const { statusBarHeight } = uni.getWindowInfo()
    const navHeight = rpx2px(88)
    const headerHeight = statusBarHeight + navHeight + "px"
    store.setHeaderHeight(headerHeight)


    // 检查更新
    const updateManager = uni.getUpdateManager()
    updateManager.onCheckForUpdate()
    updateManager.onUpdateReady(function () {
      uni.showModal({
        title: "更新提示",
        content: "新版本已经准备好，是否重启应用？",
        success: function (res) {
          if (res.confirm) {
            updateManager.applyUpdate()
          }
        },
      })
    })
  },
  onShow: function () {
    // 应用显示
  },
  onHide: function () {
    // 应用隐藏
  },
}
</script>

<style>
/*每个页面公共css */
view {
  box-sizing: border-box;
}

.gradient-bg {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(180deg, #fbfcff 0%, #e8eefb 100%);
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
}

button {
  border: none;
  outline: none;
  padding: unset;
  background: unset;
  margin: unset;
}
button::after {
  border: none;
}

button.primary {
  height: 128rpx;
  line-height: 128rpx;
  background-color: #466fff;
  border-radius: 48rpx 48rpx 48rpx 48rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(110, 113, 145, 0.12);
  font-weight: bold;
  font-size: 36rpx;
  color: #fff;
}
button.primary:disabled {
  opacity: 0.3;
}

image {
  display: block;
}

view[hidden] {
  display: none !important;
}

rich-text {
  width: 100%;
}
</style>
